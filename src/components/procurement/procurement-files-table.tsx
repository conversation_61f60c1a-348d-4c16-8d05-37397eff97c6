"use client";

import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ProcurementFile,
  PROCUREMENT_CATEGORIES,
  PROCUREMENT_STATUSES,
  formatFileSize,
  formatCurrency,
  getStatusConfig,
  getCategoryLabel,
} from "@/types/procurement";
import { MoreHorizontal, Download, Eye, Trash2, Search } from "lucide-react";

interface ProcurementFilesTableProps {
  files: ProcurementFile[];
  onFileAction: (action: string, file: ProcurementFile) => void;
}

export function ProcurementFilesTable({ files, onFileAction }: ProcurementFilesTableProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [categoryFilter, setCategoryFilter] = useState<string>("all");

  // 过滤文件
  const filteredFiles = files.filter((file) => {
    const matchesSearch = 
      file.fileName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      file.supplier.toLowerCase().includes(searchTerm.toLowerCase()) ||
      file.uploadedBy.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === "all" || file.status === statusFilter;
    const matchesCategory = categoryFilter === "all" || file.category === categoryFilter;

    return matchesSearch && matchesStatus && matchesCategory;
  });

  const handleAction = (action: string, file: ProcurementFile) => {
    onFileAction(action, file);
  };

  return (
    <div className="space-y-4">
      {/* 搜索和过滤器 */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="搜索文件名、供应商或上传者..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="状态筛选" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">所有状态</SelectItem>
            {PROCUREMENT_STATUSES.map((status) => (
              <SelectItem key={status.value} value={status.value}>
                {status.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={categoryFilter} onValueChange={setCategoryFilter}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="类别筛选" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">所有类别</SelectItem>
            {PROCUREMENT_CATEGORIES.map((category) => (
              <SelectItem key={category.value} value={category.value}>
                {category.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* 表格 */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>文件名</TableHead>
              <TableHead>类别</TableHead>
              <TableHead>供应商</TableHead>
              <TableHead>金额</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>文件大小</TableHead>
              <TableHead>上传者</TableHead>
              <TableHead>上传时间</TableHead>
              <TableHead className="w-[50px]">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredFiles.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-8 text-gray-500">
                  没有找到匹配的文件
                </TableCell>
              </TableRow>
            ) : (
              filteredFiles.map((file) => {
                const statusConfig = getStatusConfig(file.status);
                return (
                  <TableRow key={file.id}>
                    <TableCell className="font-medium">
                      <div className="max-w-[200px] truncate" title={file.fileName}>
                        {file.fileName}
                      </div>
                      {file.description && (
                        <div className="text-sm text-gray-500 max-w-[200px] truncate">
                          {file.description}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {getCategoryLabel(file.category)}
                      </Badge>
                    </TableCell>
                    <TableCell>{file.supplier}</TableCell>
                    <TableCell>
                      {file.amount ? formatCurrency(file.amount) : '-'}
                    </TableCell>
                    <TableCell>
                      <Badge className={statusConfig?.color}>
                        {statusConfig?.label}
                      </Badge>
                    </TableCell>
                    <TableCell>{formatFileSize(file.fileSize)}</TableCell>
                    <TableCell>{file.uploadedBy}</TableCell>
                    <TableCell>
                      {file.uploadDate.toLocaleDateString('zh-CN')}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleAction('view', file)}>
                            <Eye className="mr-2 h-4 w-4" />
                            查看
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleAction('download', file)}>
                            <Download className="mr-2 h-4 w-4" />
                            下载
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleAction('delete', file)}
                            className="text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            删除
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </div>

      {/* 统计信息 */}
      <div className="text-sm text-gray-500">
        显示 {filteredFiles.length} 个文件，共 {files.length} 个文件
      </div>
    </div>
  );
}
