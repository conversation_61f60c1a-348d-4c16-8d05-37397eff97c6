"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ProcurementFile,
  ProcurementConfig,
  ProcurementConfigFormData,
  REMOVE_TYPES,
} from "@/types/procurement";

interface ProcurementConfigDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  file: ProcurementFile | null;
  existingConfig?: ProcurementConfig;
  onConfigSave: (fileId: string, configData: ProcurementConfigFormData) => void;
}

export function ProcurementConfigDialog({
  open,
  onOpenChange,
  file,
  existingConfig,
  onConfigSave,
}: ProcurementConfigDialogProps) {
  const [formData, setFormData] = useState<ProcurementConfigFormData>({
    bidderCount: 0,
    removeHighestCount: 0,
    removeSecondHighestCount: 0,
    removeLowestCount: 0,
    removeSecondLowestCount: 0,
    removeType: "count",
  });

  const [errors, setErrors] = useState<Partial<ProcurementConfigFormData>>({});

  // 当弹窗打开或现有配置改变时，初始化表单数据
  useEffect(() => {
    if (open && existingConfig) {
      setFormData({
        bidderCount: existingConfig.bidderCount,
        removeHighestCount: existingConfig.removeHighestCount,
        removeSecondHighestCount: existingConfig.removeSecondHighestCount,
        removeLowestCount: existingConfig.removeLowestCount,
        removeSecondLowestCount: existingConfig.removeSecondLowestCount,
        removeType: existingConfig.removeType,
      });
    } else if (open) {
      // 重置为默认值
      setFormData({
        bidderCount: 0,
        removeHighestCount: 0,
        removeSecondHighestCount: 0,
        removeLowestCount: 0,
        removeSecondLowestCount: 0,
        removeType: "count",
      });
    }
    setErrors({});
  }, [open, existingConfig]);

  const validateForm = (): boolean => {
    // 基本验证：确保所有数值都是非负数
    // 在实际应用中，可以添加更复杂的业务逻辑验证
    return (
      formData.bidderCount >= 0 &&
      formData.removeHighestCount >= 0 &&
      formData.removeSecondHighestCount >= 0 &&
      formData.removeLowestCount >= 0 &&
      formData.removeSecondLowestCount >= 0
    );
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!file || !validateForm()) {
      return;
    }

    onConfigSave(file.id, formData);
    handleClose();
  };

  const handleClose = () => {
    setFormData({
      bidderCount: 0,
      removeHighestCount: 0,
      removeSecondHighestCount: 0,
      removeLowestCount: 0,
      removeSecondLowestCount: 0,
      removeType: "count",
    });
    setErrors({});
    onOpenChange(false);
  };

  const handleNumberChange = (
    field: keyof ProcurementConfigFormData,
    value: string
  ) => {
    const numValue = parseInt(value) || 0;
    setFormData(prev => ({
      ...prev,
      [field]: Math.max(0, numValue), // 确保不为负数
    }));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>配置采购参数</DialogTitle>
          {file && (
            <p className="text-sm text-muted-foreground">
              文件：{file.fileName}
            </p>
          )}
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 投标供应商数量 */}
          <div className="space-y-2">
            <Label htmlFor="bidderCount">投标供应商数量</Label>
            <Input
              id="bidderCount"
              type="number"
              min="0"
              value={formData.bidderCount}
              onChange={(e) => handleNumberChange('bidderCount', e.target.value)}
              placeholder="请输入投标供应商数量"
            />
          </div>

          {/* 去除最高价数量 */}
          <div className="space-y-2">
            <Label htmlFor="removeHighestCount">去除最高价数量</Label>
            <Input
              id="removeHighestCount"
              type="number"
              min="0"
              value={formData.removeHighestCount}
              onChange={(e) => handleNumberChange('removeHighestCount', e.target.value)}
              placeholder="请输入去除最高价数量"
            />
          </div>

          {/* 去除次高价数量 */}
          <div className="space-y-2">
            <Label htmlFor="removeSecondHighestCount">去除次高价数量</Label>
            <Input
              id="removeSecondHighestCount"
              type="number"
              min="0"
              value={formData.removeSecondHighestCount}
              onChange={(e) => handleNumberChange('removeSecondHighestCount', e.target.value)}
              placeholder="请输入去除次高价数量"
            />
          </div>

          {/* 去除最低价数量 */}
          <div className="space-y-2">
            <Label htmlFor="removeLowestCount">去除最低价数量</Label>
            <Input
              id="removeLowestCount"
              type="number"
              min="0"
              value={formData.removeLowestCount}
              onChange={(e) => handleNumberChange('removeLowestCount', e.target.value)}
              placeholder="请输入去除最低价数量"
            />
          </div>

          {/* 去除次低价数量 */}
          <div className="space-y-2">
            <Label htmlFor="removeSecondLowestCount">去除次低价数量</Label>
            <Input
              id="removeSecondLowestCount"
              type="number"
              min="0"
              value={formData.removeSecondLowestCount}
              onChange={(e) => handleNumberChange('removeSecondLowestCount', e.target.value)}
              placeholder="请输入去除次低价数量"
            />
          </div>

          {/* 去除类型 */}
          <div className="space-y-2">
            <Label htmlFor="removeType">去除类型</Label>
            <Select
              value={formData.removeType}
              onValueChange={(value: ProcurementConfigFormData['removeType']) =>
                setFormData(prev => ({ ...prev, removeType: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="选择去除类型" />
              </SelectTrigger>
              <SelectContent>
                {REMOVE_TYPES.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              取消
            </Button>
            <Button type="submit">
              保存配置
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
