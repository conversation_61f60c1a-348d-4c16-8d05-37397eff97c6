"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  Di<PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ProcurementFile,
  ProcurementConfig,
  ProcurementConfigItem,
  ProcurementConfigFormData,
  REMOVE_TYPES,
} from "@/types/procurement";
import { Trash2, Plus } from "lucide-react";

interface ProcurementConfigDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  file: ProcurementFile | null;
  existingConfig?: ProcurementConfig;
  onConfigSave: (fileId: string, configData: ProcurementConfigFormData) => void;
}

// 创建默认配置项
const createDefaultConfigItem = (): ProcurementConfigItem => ({
  id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
  bidderCount: 0,
  removeHighestCount: 0,
  removeSecondHighestCount: 0,
  removeLowestCount: 0,
  removeSecondLowestCount: 0,
  removeType: "count",
});

export function ProcurementConfigDialog({
  open,
  onOpenChange,
  file,
  existingConfig,
  onConfigSave,
}: ProcurementConfigDialogProps) {
  const [formData, setFormData] = useState<ProcurementConfigFormData>({
    configItems: [createDefaultConfigItem()],
  });

  // 当弹窗打开或现有配置改变时，初始化表单数据
  useEffect(() => {
    if (open && existingConfig && existingConfig.configItems.length > 0) {
      setFormData({
        configItems: existingConfig.configItems,
      });
    } else if (open) {
      // 重置为默认值
      setFormData({
        configItems: [createDefaultConfigItem()],
      });
    }
  }, [open, existingConfig]);

  const validateForm = (): boolean => {
    // 验证所有配置项的数值都是非负数
    return formData.configItems.every(item =>
      item.bidderCount >= 0 &&
      item.removeHighestCount >= 0 &&
      item.removeSecondHighestCount >= 0 &&
      item.removeLowestCount >= 0 &&
      item.removeSecondLowestCount >= 0
    );
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!file || !validateForm()) {
      return;
    }

    onConfigSave(file.id, formData);
    handleClose();
  };

  const handleClose = () => {
    setFormData({
      configItems: [createDefaultConfigItem()],
    });
    onOpenChange(false);
  };

  // 添加新的配置项
  const addConfigItem = () => {
    setFormData(prev => ({
      configItems: [...prev.configItems, createDefaultConfigItem()],
    }));
  };

  // 删除配置项
  const removeConfigItem = (index: number) => {
    if (formData.configItems.length > 1) {
      setFormData(prev => ({
        configItems: prev.configItems.filter((_, i) => i !== index),
      }));
    }
  };

  // 更新配置项的字段值
  const updateConfigItem = (
    index: number,
    field: keyof ProcurementConfigItem,
    value: string | number
  ) => {
    setFormData(prev => ({
      configItems: prev.configItems.map((item, i) =>
        i === index
          ? {
              ...item,
              [field]: typeof value === 'string' && field !== 'removeType'
                ? Math.max(0, parseInt(value) || 0)
                : value
            }
          : item
      ),
    }));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>配置采购参数</DialogTitle>
          {file && (
            <p className="text-sm text-muted-foreground">
              文件：{file.fileName}
            </p>
          )}
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 配置项列表 */}
          <div className="space-y-4">
            <div className="flex justify-between items-center pb-2 border-b">
              <Label className="text-base font-semibold text-gray-800">配置项列表</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addConfigItem}
                className="flex items-center gap-2 hover:bg-blue-50 hover:border-blue-300"
              >
                <Plus className="h-4 w-4" />
                添加配置
              </Button>
            </div>

            {formData.configItems.map((item, index) => (
              <div key={item.id} className="border rounded-lg p-4 space-y-4 bg-gray-50/50">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium text-sm text-gray-700">配置项 {index + 1}</h4>
                  {formData.configItems.length > 1 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeConfigItem(index)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {/* 投标供应商数量 */}
                  <div className="space-y-2">
                    <Label htmlFor={`bidderCount-${index}`}>投标供应商数量 (大于等于)</Label>
                    <Input
                      id={`bidderCount-${index}`}
                      type="number"
                      min="0"
                      value={item.bidderCount}
                      onChange={(e) => updateConfigItem(index, 'bidderCount', e.target.value)}
                      placeholder="0"
                    />
                  </div>

                  {/* 去除最高价数量 */}
                  <div className="space-y-2">
                    <Label htmlFor={`removeHighestCount-${index}`}>去除最高价数量</Label>
                    <Input
                      id={`removeHighestCount-${index}`}
                      type="number"
                      min="0"
                      value={item.removeHighestCount}
                      onChange={(e) => updateConfigItem(index, 'removeHighestCount', e.target.value)}
                      placeholder="0"
                    />
                  </div>

                  {/* 去除次高价数量 */}
                  <div className="space-y-2">
                    <Label htmlFor={`removeSecondHighestCount-${index}`}>去除次高价数量</Label>
                    <Input
                      id={`removeSecondHighestCount-${index}`}
                      type="number"
                      min="0"
                      value={item.removeSecondHighestCount}
                      onChange={(e) => updateConfigItem(index, 'removeSecondHighestCount', e.target.value)}
                      placeholder="0"
                    />
                  </div>

                  {/* 去除最低价数量 */}
                  <div className="space-y-2">
                    <Label htmlFor={`removeLowestCount-${index}`}>去除最低价数量</Label>
                    <Input
                      id={`removeLowestCount-${index}`}
                      type="number"
                      min="0"
                      value={item.removeLowestCount}
                      onChange={(e) => updateConfigItem(index, 'removeLowestCount', e.target.value)}
                      placeholder="0"
                    />
                  </div>

                  {/* 去除次低价数量 */}
                  <div className="space-y-2">
                    <Label htmlFor={`removeSecondLowestCount-${index}`}>去除次低价数量</Label>
                    <Input
                      id={`removeSecondLowestCount-${index}`}
                      type="number"
                      min="0"
                      value={item.removeSecondLowestCount}
                      onChange={(e) => updateConfigItem(index, 'removeSecondLowestCount', e.target.value)}
                      placeholder="0"
                    />
                  </div>

                  {/* 去除类型 */}
                  <div className="space-y-2">
                    <Label htmlFor={`removeType-${index}`}>去除类型</Label>
                    <Select
                      value={item.removeType}
                      onValueChange={(value: ProcurementConfigItem['removeType']) =>
                        updateConfigItem(index, 'removeType', value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择去除类型" />
                      </SelectTrigger>
                      <SelectContent>
                        {REMOVE_TYPES.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              取消
            </Button>
            <Button type="submit">
              保存配置
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
