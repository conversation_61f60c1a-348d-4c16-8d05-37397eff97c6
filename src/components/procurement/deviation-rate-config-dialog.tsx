"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ProcurementFile,
  DeviationRateConfig,
  DeviationRateConfigFormData,
  CHANGE_TYPES,
} from "@/types/procurement";

interface DeviationRateConfigDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  file: ProcurementFile | null;
  existingConfig?: DeviationRateConfig;
  onConfigSave: (fileId: string, configData: DeviationRateConfigFormData) => void;
}

export function DeviationRateConfigDialog({
  open,
  onOpenChange,
  file,
  existingConfig,
  onConfigSave,
}: DeviationRateConfigDialogProps) {
  const [formData, setFormData] = useState<DeviationRateConfigFormData>({
    increaseConfig: {
      percentage: 1.00,
      changeType: "decrease",
      score: 0.20,
    },
    decreaseConfig: {
      percentage: 1.00,
      changeType: "decrease",
      score: 0.10,
    },
  });

  // 当弹窗打开或现有配置改变时，初始化表单数据
  useEffect(() => {
    if (open && existingConfig) {
      setFormData({
        increaseConfig: existingConfig.increaseConfig,
        decreaseConfig: existingConfig.decreaseConfig,
      });
    } else if (open) {
      // 重置为默认值
      setFormData({
        increaseConfig: {
          percentage: 1.00,
          changeType: "decrease",
          score: 0.20,
        },
        decreaseConfig: {
          percentage: 1.00,
          changeType: "decrease",
          score: 0.10,
        },
      });
    }
  }, [open, existingConfig]);

  const validateForm = (): boolean => {
    // 验证所有数值都是非负数
    return (
      formData.increaseConfig.percentage >= 0 &&
      formData.increaseConfig.score >= 0 &&
      formData.decreaseConfig.percentage >= 0 &&
      formData.decreaseConfig.score >= 0
    );
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!file || !validateForm()) {
      return;
    }

    onConfigSave(file.id, formData);
    handleClose();
  };

  const handleClose = () => {
    setFormData({
      increaseConfig: {
        percentage: 1.00,
        changeType: "decrease",
        score: 0.20,
      },
      decreaseConfig: {
        percentage: 1.00,
        changeType: "decrease",
        score: 0.10,
      },
    });
    onOpenChange(false);
  };

  const updateIncreaseConfig = (field: keyof DeviationRateConfigFormData['increaseConfig'], value: string | number) => {
    setFormData(prev => ({
      ...prev,
      increaseConfig: {
        ...prev.increaseConfig,
        [field]: typeof value === 'string' && field !== 'changeType' 
          ? Math.max(0, parseFloat(value) || 0) 
          : value,
      },
    }));
  };

  const updateDecreaseConfig = (field: keyof DeviationRateConfigFormData['decreaseConfig'], value: string | number) => {
    setFormData(prev => ({
      ...prev,
      decreaseConfig: {
        ...prev.decreaseConfig,
        [field]: typeof value === 'string' && field !== 'changeType' 
          ? Math.max(0, parseFloat(value) || 0) 
          : value,
      },
    }));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>偏差率配置</DialogTitle>
          {file && (
            <p className="text-sm text-muted-foreground">
              文件：{file.fileName}
            </p>
          )}
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 有效投标报价宜增加配置 */}
          <div className="space-y-4">
            <h3 className="text-base font-semibold text-gray-800 border-b pb-2">
              有效投标报价每增加
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="increase-percentage">百分比值</Label>
                <div className="flex items-center space-x-2">
                  <Input
                    id="increase-percentage"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.increaseConfig.percentage}
                    onChange={(e) => updateIncreaseConfig('percentage', e.target.value)}
                    className="flex-1"
                  />
                  <span className="text-sm text-gray-500 w-4">%</span>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="increase-change-type">分值操作</Label>
                <div className="flex items-center space-x-2">
                  <Select
                    value={formData.increaseConfig.changeType}
                    onValueChange={(value: 'increase' | 'decrease') =>
                      updateIncreaseConfig('changeType', value)
                    }
                  >
                    <SelectTrigger className="flex-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {CHANGE_TYPES.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <span className="text-sm text-gray-500 w-4"></span>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="increase-score">分值</Label>
                <div className="flex items-center space-x-2">
                  <Input
                    id="increase-score"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.increaseConfig.score}
                    onChange={(e) => updateIncreaseConfig('score', e.target.value)}
                    className="flex-1"
                  />
                  <span className="text-sm text-gray-500 w-4">分</span>
                </div>
              </div>
            </div>
          </div>

          {/* 有效投标报价宜减少配置 */}
          <div className="space-y-4">
            <h3 className="text-base font-semibold text-gray-800 border-b pb-2">
              有效投标报价每减少
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="decrease-percentage">百分比值</Label>
                <div className="flex items-center space-x-2">
                  <Input
                    id="decrease-percentage"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.decreaseConfig.percentage}
                    onChange={(e) => updateDecreaseConfig('percentage', e.target.value)}
                    className="flex-1"
                  />
                  <span className="text-sm text-gray-500 w-4">%</span>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="decrease-change-type">分值操作</Label>
                <div className="flex items-center space-x-2">
                  <Select
                    value={formData.decreaseConfig.changeType}
                    onValueChange={(value: 'increase' | 'decrease') =>
                      updateDecreaseConfig('changeType', value)
                    }
                  >
                    <SelectTrigger className="flex-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {CHANGE_TYPES.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <span className="text-sm text-gray-500 w-4"></span>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="decrease-score">分值</Label>
                <div className="flex items-center space-x-2">
                  <Input
                    id="decrease-score"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.decreaseConfig.score}
                    onChange={(e) => updateDecreaseConfig('score', e.target.value)}
                    className="flex-1"
                  />
                  <span className="text-sm text-gray-500 w-4">分</span>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              取消
            </Button>
            <Button type="submit">
              保存配置
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
