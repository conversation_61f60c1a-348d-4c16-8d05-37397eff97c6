"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  ProcurementFile,
  ProcurementFileFormData,
  ProcurementConfig,
  ProcurementConfigFormData,
  mockProcurementFiles,
  PROCUREMENT_STATUSES,
  formatCurrency,
} from "@/types/procurement";
import { ProcurementFilesTable } from "./procurement-files-table";
import { FileUploadDialog } from "./file-upload-dialog";
import { ProcurementConfigDialog } from "./procurement-config-dialog";
import { Plus, FileText, TrendingUp, Clock, CheckCircle } from "lucide-react";

export function ProcurementManagement() {
  const [files, setFiles] = useState<ProcurementFile[]>(mockProcurementFiles);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [configDialogOpen, setConfigDialogOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<ProcurementFile | null>(null);
  const [configs, setConfigs] = useState<ProcurementConfig[]>([]);

  // 统计数据
  const totalFiles = files.length;
  const pendingFiles = files.filter(f => f.status === 'pending').length;
  const approvedFiles = files.filter(f => f.status === 'approved').length;
  const totalAmount = files
    .filter(f => f.amount && f.status === 'approved')
    .reduce((sum, f) => sum + (f.amount || 0), 0);

  const handleFileUpload = (fileData: ProcurementFileFormData, file: File) => {
    const newFile: ProcurementFile = {
      id: Date.now().toString(),
      fileName: fileData.fileName,
      fileType: file.type,
      fileSize: file.size,
      uploadDate: new Date(),
      status: 'pending',
      category: fileData.category,
      supplier: fileData.supplier,
      amount: fileData.amount,
      description: fileData.description,
      uploadedBy: '当前用户', // 在实际应用中应该从用户上下文获取
      lastModified: new Date(),
    };

    setFiles(prev => [newFile, ...prev]);
  };

  const handleFileAction = (action: string, file: ProcurementFile) => {
    switch (action) {
      case 'view':
        // 在实际应用中，这里会打开文件预览
        alert(`查看文件: ${file.fileName}`);
        break;
      case 'download':
        // 在实际应用中，这里会下载文件
        alert(`下载文件: ${file.fileName}`);
        break;
      case 'config':
        setSelectedFile(file);
        setConfigDialogOpen(true);
        break;
      case 'delete':
        if (confirm(`确定要删除文件 "${file.fileName}" 吗？`)) {
          setFiles(prev => prev.filter(f => f.id !== file.id));
          // 同时删除相关配置
          setConfigs(prev => prev.filter(c => c.fileId !== file.id));
        }
        break;
      default:
        break;
    }
  };

  const handleConfigSave = (fileId: string, configData: ProcurementConfigFormData) => {
    const existingConfigIndex = configs.findIndex(c => c.fileId === fileId);

    if (existingConfigIndex >= 0) {
      // 更新现有配置
      setConfigs(prev => prev.map((config, index) =>
        index === existingConfigIndex
          ? {
              ...config,
              ...configData,
              updatedAt: new Date(),
            }
          : config
      ));
    } else {
      // 创建新配置
      const newConfig: ProcurementConfig = {
        id: Date.now().toString(),
        fileId,
        ...configData,
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: '当前用户', // 在实际应用中应该从用户上下文获取
      };
      setConfigs(prev => [...prev, newConfig]);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">采购文件管理</h1>
          <p className="text-muted-foreground">
            管理和跟踪所有采购相关文件
          </p>
        </div>
        <Button onClick={() => setUploadDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          上传文件
        </Button>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总文件数</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalFiles}</div>
            <p className="text-xs text-muted-foreground">
              所有上传的文件
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">待处理</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendingFiles}</div>
            <p className="text-xs text-muted-foreground">
              需要审核的文件
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已批准</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{approvedFiles}</div>
            <p className="text-xs text-muted-foreground">
              已通过审核的文件
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总金额</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalAmount)}</div>
            <p className="text-xs text-muted-foreground">
              已批准的采购金额
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 状态分布 */}
      <Card>
        <CardHeader>
          <CardTitle>文件状态分布</CardTitle>
          <CardDescription>
            各状态文件的数量分布
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            {PROCUREMENT_STATUSES.map((status) => {
              const count = files.filter(f => f.status === status.value).length;
              return (
                <div key={status.value} className="flex items-center space-x-2">
                  <Badge className={status.color}>
                    {status.label}
                  </Badge>
                  <span className="text-sm text-muted-foreground">
                    {count} 个文件
                  </span>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* 文件列表 */}
      <Card>
        <CardHeader>
          <CardTitle>文件列表</CardTitle>
          <CardDescription>
            查看和管理所有采购文件
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ProcurementFilesTable 
            files={files} 
            onFileAction={handleFileAction}
          />
        </CardContent>
      </Card>

      {/* 文件上传对话框 */}
      <FileUploadDialog
        open={uploadDialogOpen}
        onOpenChange={setUploadDialogOpen}
        onFileUpload={handleFileUpload}
      />

      {/* 配置对话框 */}
      <ProcurementConfigDialog
        open={configDialogOpen}
        onOpenChange={setConfigDialogOpen}
        file={selectedFile}
        existingConfig={selectedFile ? configs.find(c => c.fileId === selectedFile.id) : undefined}
        onConfigSave={handleConfigSave}
      />
    </div>
  );
}
