import { SparklesText } from "@/components/ui/sparkles-text";

export function SparklesTextDemo() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-slate-900 to-slate-800 p-8">
      <div className="space-y-8 text-center">
        <SparklesText text="Magic UI" />
        
        <SparklesText 
          text="Custom Colors" 
          colors={{ first: "#FF6B6B", second: "#4ECDC4" }}
          className="text-4xl"
        />
        
        <SparklesText 
          text="More Sparkles!" 
          sparklesCount={20}
          colors={{ first: "#FFD93D", second: "#6BCF7F" }}
          className="text-5xl"
        />
        
        <div className="mt-12 text-white/70 text-lg">
          <p>✨ Animated sparkles with framer-motion</p>
          <p>🎨 Customizable colors and sparkle count</p>
          <p>⚡ Built with shadcn/ui and Tailwind CSS</p>
        </div>
      </div>
    </div>
  );
}
