"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { FileText, Sparkles, Home } from "lucide-react";

const navigationItems = [
  {
    href: "/sparkles",
    label: "SparklesText",
    icon: Sparkles,
    description: "动画文字效果演示",
  },
  {
    href: "/procurement",
    label: "采购管理",
    icon: FileText,
    description: "文件管理系统",
  },
];

export function Navigation() {
  const pathname = usePathname();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 to-slate-800">
      <div className="container mx-auto p-6">
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Sparkles className="h-8 w-8 text-purple-400 mr-2" />
            <h1 className="text-3xl font-bold text-white">管理系统演示</h1>
          </div>
          <p className="text-gray-300">
            基于 Next.js + shadcn/ui 构建的现代化管理系统
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 max-w-4xl mx-auto">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            const isActive = pathname === item.href;
            
            return (
              <Link key={item.href} href={item.href}>
                <Card className={`transition-all duration-200 hover:scale-105 cursor-pointer ${
                  isActive 
                    ? "ring-2 ring-purple-500 bg-purple-50 dark:bg-purple-900/20" 
                    : "hover:shadow-lg"
                }`}>
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className={`p-3 rounded-lg ${
                        isActive 
                          ? "bg-purple-500 text-white" 
                          : "bg-gray-100 text-gray-600"
                      }`}>
                        <Icon className="h-6 w-6" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold">{item.label}</h3>
                        <p className="text-sm text-gray-600">{item.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            );
          })}
        </div>

        <div className="text-center mt-12">
          <p className="text-gray-400 text-sm">
            ✨ 使用 framer-motion 动画效果 | 🎨 shadcn/ui 组件库 | ⚡ Next.js 15 框架
          </p>
        </div>
      </div>
    </div>
  );
}
