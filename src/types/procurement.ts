export interface ProcurementFile {
  id: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  uploadDate: Date;
  status: 'pending' | 'approved' | 'rejected' | 'under_review';
  category: 'contract' | 'invoice' | 'quotation' | 'specification' | 'other';
  supplier: string;
  amount?: number;
  description?: string;
  uploadedBy: string;
  lastModified: Date;
}

export interface ProcurementFileFormData {
  fileName: string;
  category: ProcurementFile['category'];
  supplier: string;
  amount?: number;
  description?: string;
}

export const PROCUREMENT_CATEGORIES = [
  { value: 'contract', label: '合同' },
  { value: 'invoice', label: '发票' },
  { value: 'quotation', label: '报价单' },
  { value: 'specification', label: '技术规格' },
  { value: 'other', label: '其他' },
] as const;

export const PROCUREMENT_STATUSES = [
  { value: 'pending', label: '待处理', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'under_review', label: '审核中', color: 'bg-blue-100 text-blue-800' },
  { value: 'approved', label: '已批准', color: 'bg-green-100 text-green-800' },
  { value: 'rejected', label: '已拒绝', color: 'bg-red-100 text-red-800' },
] as const;

// 模拟数据
export const mockProcurementFiles: ProcurementFile[] = [
  {
    id: '1',
    fileName: '供应商A_合同_2024.pdf',
    fileType: 'application/pdf',
    fileSize: 2048576, // 2MB
    uploadDate: new Date('2024-01-15'),
    status: 'approved',
    category: 'contract',
    supplier: '供应商A',
    amount: 150000,
    description: '年度采购合同',
    uploadedBy: '张三',
    lastModified: new Date('2024-01-16'),
  },
  {
    id: '2',
    fileName: '设备报价单_供应商B.xlsx',
    fileType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    fileSize: 1024000, // 1MB
    uploadDate: new Date('2024-01-20'),
    status: 'under_review',
    category: 'quotation',
    supplier: '供应商B',
    amount: 85000,
    description: '办公设备采购报价',
    uploadedBy: '李四',
    lastModified: new Date('2024-01-20'),
  },
  {
    id: '3',
    fileName: '发票_202401_供应商C.pdf',
    fileType: 'application/pdf',
    fileSize: 512000, // 512KB
    uploadDate: new Date('2024-01-25'),
    status: 'pending',
    category: 'invoice',
    supplier: '供应商C',
    amount: 25000,
    description: '1月份服务费发票',
    uploadedBy: '王五',
    lastModified: new Date('2024-01-25'),
  },
  {
    id: '4',
    fileName: '技术规格书_新系统.docx',
    fileType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    fileSize: 3072000, // 3MB
    uploadDate: new Date('2024-01-28'),
    status: 'rejected',
    category: 'specification',
    supplier: '供应商D',
    description: '新系统技术要求文档',
    uploadedBy: '赵六',
    lastModified: new Date('2024-01-29'),
  },
  {
    id: '5',
    fileName: '采购申请表_办公用品.pdf',
    fileType: 'application/pdf',
    fileSize: 256000, // 256KB
    uploadDate: new Date('2024-02-01'),
    status: 'approved',
    category: 'other',
    supplier: '供应商E',
    amount: 5000,
    description: '办公用品采购申请',
    uploadedBy: '孙七',
    lastModified: new Date('2024-02-01'),
  },
];

// 工具函数
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
  }).format(amount);
}

export function getStatusConfig(status: ProcurementFile['status']) {
  return PROCUREMENT_STATUSES.find(s => s.value === status);
}

export function getCategoryLabel(category: ProcurementFile['category']) {
  return PROCUREMENT_CATEGORIES.find(c => c.value === category)?.label || category;
}
