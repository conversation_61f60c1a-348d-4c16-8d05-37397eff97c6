import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "SparklesText Demo",
  description: "A demo of the SparklesText component with framer-motion",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className="antialiased">
        {children}
      </body>
    </html>
  );
}
