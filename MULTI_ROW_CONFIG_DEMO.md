# 多行配置功能演示

## 功能演示步骤

### 1. 访问采购文件管理页面
- 打开浏览器访问：`http://localhost:3001/procurement`
- 页面显示采购文件列表和统计信息

### 2. 打开配置弹窗
- 在文件列表中找到任意一个文件
- 点击该文件行末尾的操作按钮（三个点图标）
- 在下拉菜单中选择"配置"选项
- 配置弹窗将会打开

### 3. 默认配置项
- 弹窗打开时会显示一个默认的配置项
- 配置项包含以下字段：
  - 投标供应商数量
  - 去除最高价数量
  - 去除次高价数量
  - 去除最低价数量
  - 去除次低价数量
  - 去除类型（下拉选择）

### 4. 添加更多配置项
- 点击右上角的"添加配置"按钮
- 系统会新增一个配置项卡片
- 新配置项包含所有相同的字段
- 可以为每个配置项设置不同的参数值

### 5. 删除配置项
- 当有多个配置项时，每个配置项右上角会显示删除按钮（垃圾桶图标）
- 点击删除按钮可以移除该配置项
- 系统至少保留一个配置项（最后一个配置项无法删除）

### 6. 编辑配置项
- 直接在输入框中修改数值
- 在下拉菜单中选择不同的去除类型
- 所有数值字段都会自动验证（确保非负数）

### 7. 保存配置
- 填写完所有配置项后，点击"保存配置"按钮
- 系统会保存所有配置项的数据
- 弹窗关闭，配置保存成功

### 8. 查看已保存的配置
- 再次点击同一文件的"配置"选项
- 弹窗会显示之前保存的所有配置项
- 可以继续编辑、添加或删除配置项

## 界面特点

### 响应式布局
- 在大屏幕上：字段以3列网格布局显示
- 在中等屏幕上：字段以2列网格布局显示
- 在小屏幕上：字段以1列布局显示

### 视觉设计
- 每个配置项以卡片形式展示，有浅灰色背景
- 配置项之间有明显的视觉分隔
- 添加按钮使用蓝色主题，删除按钮使用红色主题
- 字段标签清晰，输入框有合适的占位符

### 用户体验
- 操作直观，按钮功能明确
- 实时验证输入数据
- 支持键盘导航
- 弹窗大小适中，内容可滚动

## 数据结构

### 配置项数据结构
```typescript
interface ProcurementConfigItem {
  id: string;
  bidderCount: number;
  removeHighestCount: number;
  removeSecondHighestCount: number;
  removeLowestCount: number;
  removeSecondLowestCount: number;
  removeType: 'count' | 'percentage_round' | 'percentage_floor';
}
```

### 完整配置数据结构
```typescript
interface ProcurementConfig {
  id: string;
  fileId: string;
  configItems: ProcurementConfigItem[];
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}
```

## 测试场景

### 基本功能测试
1. 打开配置弹窗 ✓
2. 填写单个配置项 ✓
3. 保存配置 ✓
4. 重新打开查看配置 ✓

### 多行配置测试
1. 添加多个配置项 ✓
2. 为不同配置项设置不同参数 ✓
3. 删除中间的配置项 ✓
4. 保存多个配置项 ✓

### 边界情况测试
1. 尝试删除最后一个配置项（应该被阻止）✓
2. 输入负数（应该自动转为0）✓
3. 输入非数字字符（应该被过滤）✓
4. 不填写任何数据直接保存（应该保存默认值）✓

## 后续改进建议

1. **配置项标题自定义** - 允许用户为每个配置项设置自定义名称
2. **配置项复制** - 添加复制配置项的功能
3. **配置项排序** - 支持拖拽排序配置项
4. **批量操作** - 支持批量删除或批量编辑
5. **配置模板** - 预设常用的配置模板
6. **导入导出** - 支持配置数据的导入导出功能
