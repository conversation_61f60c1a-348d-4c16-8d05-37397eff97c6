# 采购文件配置功能（多行配置版本）

## 功能概述

在采购文件管理系统中新增了多行配置功能，允许用户为每个采购文件设置多个投标参数配置条目。每个文件可以有多个不同的配置方案，支持动态添加和删除配置项。

## 功能特性

### 多行配置支持

- **多配置项** - 每个文件可以设置多个配置条目
- **动态管理** - 支持添加、删除配置项
- **独立配置** - 每个配置项包含完整的参数设置

### 每个配置项包含的字段

1. **投标供应商数量** - 设置参与投标的供应商总数
2. **去除最高价数量** - 设置需要去除的最高价投标数量
3. **去除次高价数量** - 设置需要去除的次高价投标数量
4. **去除最低价数量** - 设置需要去除的最低价投标数量
5. **去除次低价数量** - 设置需要去除的次低价投标数量
6. **去除类型** - 选择去除方式：
   - 数量：按具体数量去除
   - 百分比（四舍五入）：按百分比计算，结果四舍五入
   - 百分比（取整）：按百分比计算，结果向下取整

### 界面特性

- **卡片式布局** - 每个配置项以卡片形式展示
- **响应式设计** - 字段自动适配屏幕尺寸（1-3列布局）
- **直观操作** - 清晰的添加/删除按钮
- **视觉区分** - 不同配置项有明显的视觉分隔

## 使用方法

### 1. 访问配置功能

1. 进入采购文件管理页面：`/procurement`
2. 在文件列表中找到需要配置的文件
3. 点击文件行末尾的操作按钮（三个点图标）
4. 在下拉菜单中选择"配置"选项

### 2. 配置参数

1. 在弹出的配置对话框中，默认会显示一个配置项
2. 填写第一个配置项的各项参数
3. 如需添加更多配置，点击"添加配置"按钮
4. 每个配置项都包含完整的参数设置
5. 所有数值字段都必须为非负整数
6. 为每个配置项选择合适的去除类型
7. 点击"保存配置"按钮完成设置

### 3. 管理配置项

- **添加配置项** - 点击"添加配置"按钮新增配置项
- **删除配置项** - 点击配置项右上角的删除按钮（至少保留一个配置项）
- **编辑配置项** - 直接修改任意配置项的字段值

### 4. 修改配置

- 如果文件已有配置，再次点击"配置"会显示所有现有配置项
- 可以修改、添加或删除配置项
- 配置会自动更新时间戳

## 技术实现

### 新增组件

1. **ProcurementConfigDialog** - 配置弹窗组件
   - 位置：`src/components/procurement/procurement-config-dialog.tsx`
   - 功能：提供配置表单界面

### 新增类型定义

1. **ProcurementConfig** - 配置数据结构
2. **ProcurementConfigFormData** - 配置表单数据结构
3. **REMOVE_TYPES** - 去除类型选项常量

### 修改的组件

1. **ProcurementFilesTable** - 文件列表组件
   - 新增配置操作菜单项
   - 添加Settings图标

2. **ProcurementManagement** - 主管理组件
   - 新增配置状态管理
   - 新增配置保存逻辑
   - 集成配置弹窗

## 数据存储

当前实现使用组件内部状态存储配置数据。在生产环境中，建议：

1. 将配置数据持久化到数据库
2. 实现配置的增删改查API
3. 添加配置历史记录功能
4. 实现配置的导入导出功能

## 验证规则

- 所有数值字段必须为非负整数
- 去除数量不应超过投标供应商总数
- 百分比计算时会自动处理边界情况

## 后续扩展

可以考虑添加以下功能：

1. 配置模板功能
2. 批量配置功能
3. 配置审批流程
4. 配置效果预览
5. 配置统计分析
