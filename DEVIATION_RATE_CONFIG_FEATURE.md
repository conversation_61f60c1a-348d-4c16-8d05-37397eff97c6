# 偏差率配置功能

## 功能概述

在采购文件管理系统中新增了偏差率配置功能，允许用户为每个采购文件设置投标报价的偏差率参数。该功能用于配置有效投标报价的增减幅度和对应的评分规则。

## 功能特性

### 配置项目

1. **有效投标报价宜增加配置**
   - 百分比值：设置增加的百分比
   - 增减类型：选择"增"或"减"
   - 分值：对应的评分分值

2. **有效投标报价宜减少配置**
   - 百分比值：设置减少的百分比
   - 增减类型：选择"增"或"减"
   - 分值：对应的评分分值

### 界面特性

- **清晰的分组布局** - 增加和减少配置分别显示
- **响应式设计** - 字段自动适配屏幕尺寸（1-3列布局）
- **直观的单位显示** - 百分比和分值单位清晰标注
- **数据验证** - 确保输入数据的有效性

## 使用方法

### 1. 访问偏差率配置功能

1. 进入采购文件管理页面：`/procurement`
2. 在文件列表中找到需要配置的文件
3. 点击文件行末尾的操作按钮（三个点图标）
4. 在下拉菜单中选择"偏差率配置"选项

### 2. 配置参数

#### 有效投标报价宜增加配置
1. **百分比值** - 输入增加的百分比数值（如：1.00）
2. **增减类型** - 从下拉菜单选择"增"或"减"
3. **分值** - 输入对应的评分分值（如：0.20）

#### 有效投标报价宜减少配置
1. **百分比值** - 输入减少的百分比数值（如：1.00）
2. **增减类型** - 从下拉菜单选择"增"或"减"
3. **分值** - 输入对应的评分分值（如：0.10）

### 3. 保存配置

- 填写完所有配置参数后，点击"保存配置"按钮
- 系统会保存偏差率配置数据
- 弹窗关闭，配置保存成功

### 4. 修改配置

- 如果文件已有偏差率配置，再次点击"偏差率配置"会显示现有配置数据
- 可以修改任意字段并重新保存
- 配置会自动更新时间戳

## 默认配置值

系统提供以下默认配置值：

### 有效投标报价宜增加
- 百分比值：1.00%
- 增减类型：减
- 分值：0.20分

### 有效投标报价宜减少
- 百分比值：1.00%
- 增减类型：减
- 分值：0.10分

## 技术实现

### 新增组件

1. **DeviationRateConfigDialog** - 偏差率配置弹窗组件
   - 位置：`src/components/procurement/deviation-rate-config-dialog.tsx`
   - 功能：提供偏差率配置表单界面

### 新增类型定义

1. **DeviationRateConfig** - 偏差率配置数据结构
2. **DeviationRateConfigFormData** - 偏差率配置表单数据结构
3. **CHANGE_TYPES** - 增减类型选项常量

### 修改的组件

1. **ProcurementFilesTable** - 文件列表组件
   - 新增偏差率配置操作菜单项
   - 添加TrendingUp图标

2. **ProcurementManagement** - 主管理组件
   - 新增偏差率配置状态管理
   - 新增偏差率配置保存逻辑
   - 集成偏差率配置弹窗

## 数据结构

### 偏差率配置数据结构
```typescript
interface DeviationRateConfig {
  id: string;
  fileId: string;
  increaseConfig: {
    percentage: number;
    changeType: 'increase' | 'decrease';
    score: number;
  };
  decreaseConfig: {
    percentage: number;
    changeType: 'increase' | 'decrease';
    score: number;
  };
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}
```

### 表单数据结构
```typescript
interface DeviationRateConfigFormData {
  increaseConfig: {
    percentage: number;
    changeType: 'increase' | 'decrease';
    score: number;
  };
  decreaseConfig: {
    percentage: number;
    changeType: 'increase' | 'decrease';
    score: number;
  };
}
```

## 验证规则

- 所有数值字段必须为非负数
- 百分比值支持小数点后两位
- 分值支持小数点后两位
- 增减类型必须从预定义选项中选择

## 使用场景

1. **投标评估** - 设置投标报价的合理偏差范围
2. **评分标准** - 定义不同偏差程度的评分规则
3. **风险控制** - 控制投标报价的波动范围
4. **公平竞争** - 确保投标过程的公平性和透明度

## 后续扩展

可以考虑添加以下功能：

1. **配置模板** - 预设常用的偏差率配置模板
2. **批量配置** - 支持批量设置多个文件的偏差率配置
3. **配置历史** - 记录配置变更历史
4. **配置审批** - 添加配置审批流程
5. **配置导入导出** - 支持配置数据的导入导出功能
6. **配置效果预览** - 预览配置对评分的影响

## 注意事项

1. 偏差率配置与普通配置是独立的，互不影响
2. 每个文件只能有一个偏差率配置
3. 配置数据在组件状态中保存，生产环境建议持久化到数据库
4. 建议定期备份配置数据以防丢失
